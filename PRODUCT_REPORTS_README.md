# eBay Product Report Generator

This system generates comprehensive research reports for eBay products using AI-powered browser automation and Google Vertex AI.

## Features

- **CSV Input Processing**: Reads eBay product metadata from CSV files
- **Sequential Processing**: Processes products one at a time to avoid rate limiting
- **Comprehensive Research**: Performs thorough web research for each product
- **Token Tracking**: Detailed logging of LLM token usage and cost estimates
- **Structured Reports**: Generates .txt reports with consistent structure (max 2000 tokens)
- **Batch Processing**: Handles multiple products with summary reporting

## Setup

### Prerequisites

1. **Node.js and npm** (for the automation system)
2. **Python 3.7+** (for the main entry point)
3. **Google Cloud Project** with Vertex AI enabled

### Environment Variables

Set the following environment variables:

```bash
# Required
export GOOGLE_CLOUD_PROJECT_ID=your-google-cloud-project-id

# Optional (if not using default application credentials)
export GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json

# LLM Configuration (automatically set to vertex-ai)
export LLM_PROVIDER=vertex-ai
export LLM_MODEL=gemini-1.5-pro
export GOOGLE_CLOUD_LOCATION=us-central1
```

### Installation

1. Clone the repository and navigate to the project directory
2. Run the Python script (it will automatically install dependencies):

```bash
python main.py sample_products.csv
```

Or manually install dependencies:

```bash
npm install
npm run build
```

## Usage

### Basic Usage

```bash
python main.py <csv_file> [output_directory]
```

### Examples

```bash
# Process products and save to default ./reports directory
python main.py products.csv

# Process products and save to custom directory
python main.py products.csv ./my_reports

# Skip dependency installation and build (if already done)
python main.py products.csv --skip-deps --skip-build
```

### CSV Format

Your CSV file must contain the following columns:

**Required:**
- `itemId`: eBay item identifier
- `title`: Product title

**Optional (will be marked as N/A if missing):**
- `price`: Product price
- `currency`: Currency code (e.g., USD)
- `condition`: Product condition
- `mainImage`: URL to main product image
- `shortDescription`: Brief product description
- `additionalImages`: URLs to additional images
- `itemSpecifics`: Product specifications
- `seller_username`: eBay seller username
- `seller_feedback_pct`: Seller feedback percentage
- `seller_feedback_count`: Number of seller feedback reviews
- `location_city`: Seller location city
- `location_country`: Seller location country

### Sample CSV

See `sample_products.csv` for an example format.

## Output

### Report Files

Each product generates a `.txt` report file with the following structure:

1. **Product Overview**: Detailed description and specifications
2. **Market Analysis**: Price comparisons and market positioning
3. **Reviews & Reputation**: User reviews and expert opinions
4. **Issues and Concerns**: Known problems, counterfeits, warranties
5. **Seller and Listing Evaluation**: Seller reputation and listing reliability
6. **Conclusion**: Summary of strengths, weaknesses, and value
7. **Citations**: All sources used in research

### Token Usage Logs

Detailed token usage logs are saved to `logs/token-usage/` including:
- Per-product token consumption
- Cost estimates
- Request counts
- Batch summaries

### Batch Summary

A batch summary file is generated showing:
- Total products processed
- Success/failure counts
- Token usage totals
- Cost estimates

## Configuration

### Research Depth

The system is configured for "unimaginably thorough" research as requested:
- No token limits on research depth
- Multiple search strategies
- Cross-referencing of information
- Comprehensive fact-checking

### Rate Limiting

- 2-second delay between products
- Single browser session for all products
- Respectful of website rate limits

### Error Handling

- Graceful handling of missing data
- Retry logic for network issues
- Detailed error logging
- Partial batch completion support

## Troubleshooting

### Common Issues

1. **Missing Google Cloud credentials**:
   ```
   Error: GOOGLE_CLOUD_PROJECT_ID environment variable is required
   ```
   Solution: Set the required environment variables

2. **CSV validation errors**:
   ```
   Error: Missing required columns: ['itemId', 'title']
   ```
   Solution: Ensure your CSV has the required columns

3. **Node.js dependencies**:
   ```
   Error: npm not found
   ```
   Solution: Install Node.js and npm

4. **Build errors**:
   ```
   Error building project
   ```
   Solution: Check TypeScript compilation errors and fix syntax issues

### Logs

Check the following for debugging:
- Console output during execution
- `logs/token-usage/` for token usage details
- Browser automation logs (if enabled)

## Cost Estimation

Token usage is tracked and costs are estimated based on Vertex AI pricing:
- Input tokens: ~$0.00025 per 1K tokens
- Output tokens: ~$0.0005 per 1K tokens

Actual costs may vary based on current Google Cloud pricing.

## Advanced Usage

### Direct TypeScript Usage

You can also run the report generator directly:

```bash
npm run build
npm run product-reports products.csv ./reports
```

### Custom Research Prompts

Modify `researcher-prompt.txt` to customize the research approach and report structure.

### Integration

The system can be integrated into larger workflows by importing the TypeScript modules:

```typescript
import { ProductReportGenerator } from './src/core/productReportGenerator.js';

const generator = new ProductReportGenerator('./output');
const results = await generator.generateReportsFromCSV('./products.csv');
```
