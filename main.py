#!/usr/bin/env python3
"""
eBay Product Report Generator

This script processes a CSV file containing eBay product metadata and generates
comprehensive research reports for each product using an AI-powered browser automation system.

Usage:
    python main.py <csv_file> [output_directory]

Example:
    python main.py products.csv ./reports

CSV Format:
The CSV file should contain the following columns:
- itemId: eBay item identifier
- title: Product title
- price: Product price
- currency: Currency code (e.g., USD)
- condition: Product condition
- mainImage: URL to main product image
- shortDescription: Brief product description
- additionalImages: URLs to additional images
- itemSpecifics: Product specifications
- seller_username: eBay seller username
- seller_feedback_pct: Seller feedback percentage
- seller_feedback_count: Number of seller feedback reviews
- location_city: Seller location city
- location_country: Seller location country

Environment Variables Required:
- GOOGLE_CLOUD_PROJECT_ID: Your Google Cloud project ID for Vertex AI
- GOOGLE_APPLICATION_CREDENTIALS: Path to your Google Cloud service account key file

Output:
- Individual .txt report files for each product (max 2000 tokens each)
- Batch summary report
- Token usage logs with cost estimates
"""

import sys
import os
import subprocess
import json
import csv
from pathlib import Path
from typing import List, Dict, Any
import argparse

def validate_csv_file(csv_path: str) -> bool:
    """Validate that the CSV file exists and has the required structure."""
    if not os.path.exists(csv_path):
        print(f"Error: CSV file not found: {csv_path}")
        return False
    
    required_columns = ['itemId', 'title']
    expected_columns = [
        'itemId', 'title', 'price', 'currency', 'condition',
        'mainImage', 'shortDescription', 'additionalImages',
        'itemSpecifics', 'seller_username', 'seller_feedback_pct',
        'seller_feedback_count', 'location_city', 'location_country'
    ]
    
    try:
        with open(csv_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            headers = reader.fieldnames or []
            
            # Check for required columns
            missing_required = [col for col in required_columns if col not in headers]
            if missing_required:
                print(f"Error: Missing required columns: {missing_required}")
                return False
            
            # Check for expected columns
            missing_expected = [col for col in expected_columns if col not in headers]
            if missing_expected:
                print(f"Warning: Missing expected columns: {missing_expected}")
                print("These columns will be treated as 'N/A' in the reports.")
            
            # Count rows
            row_count = sum(1 for _ in reader)
            if row_count == 0:
                print("Error: CSV file is empty")
                return False
            
            print(f"CSV validation passed: {row_count} products found")
            return True
            
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return False

def validate_environment() -> bool:
    """Validate that required environment variables are set."""
    required_vars = ['GOOGLE_CLOUD_PROJECT_ID']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"Error: Missing required environment variables: {missing_vars}")
        print("\nPlease set the following environment variables:")
        for var in missing_vars:
            if var == 'GOOGLE_CLOUD_PROJECT_ID':
                print(f"  export {var}=your-google-cloud-project-id")
        print("\nOptionally, also set:")
        print("  export GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json")
        return False
    
    return True

def check_node_dependencies() -> bool:
    """Check if Node.js dependencies are installed."""
    try:
        result = subprocess.run(['npm', 'list'], 
                              capture_output=True, text=True, cwd=Path(__file__).parent)
        return result.returncode == 0
    except FileNotFoundError:
        print("Error: npm not found. Please install Node.js and npm.")
        return False

def install_dependencies() -> bool:
    """Install Node.js dependencies if needed."""
    print("Installing Node.js dependencies...")
    try:
        result = subprocess.run(['npm', 'install'], 
                              cwd=Path(__file__).parent, 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Error installing dependencies: {result.stderr}")
            return False
        print("Dependencies installed successfully.")
        return True
    except Exception as e:
        print(f"Error installing dependencies: {e}")
        return False

def build_typescript() -> bool:
    """Build the TypeScript project."""
    print("Building TypeScript project...")
    try:
        result = subprocess.run(['npm', 'run', 'build'], 
                              cwd=Path(__file__).parent, 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Error building project: {result.stderr}")
            return False
        print("TypeScript build completed successfully.")
        return True
    except Exception as e:
        print(f"Error building project: {e}")
        return False

def run_report_generation(csv_path: str, output_dir: str = None) -> bool:
    """Run the Node.js report generation process."""
    cmd = ['node', 'dist/productReportRunner.js', csv_path]
    if output_dir:
        cmd.append(output_dir)
    
    print(f"Starting report generation...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        # Run the Node.js process
        result = subprocess.run(cmd, 
                              cwd=Path(__file__).parent,
                              text=True)
        
        if result.returncode == 0:
            print("Report generation completed successfully!")
            return True
        else:
            print(f"Report generation failed with exit code: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"Error running report generation: {e}")
        return False

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Generate comprehensive reports for eBay products from CSV metadata",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    parser.add_argument('csv_file', help='Path to CSV file containing product metadata')
    parser.add_argument('output_dir', nargs='?', help='Output directory for reports (default: ./reports)')
    parser.add_argument('--skip-build', action='store_true', help='Skip TypeScript build step')
    parser.add_argument('--skip-deps', action='store_true', help='Skip dependency installation')
    
    args = parser.parse_args()
    
    print("eBay Product Report Generator")
    print("=" * 50)
    
    # Validate environment
    if not validate_environment():
        sys.exit(1)
    
    # Validate CSV file
    if not validate_csv_file(args.csv_file):
        sys.exit(1)
    
    # Check and install dependencies
    if not args.skip_deps:
        if not check_node_dependencies():
            if not install_dependencies():
                sys.exit(1)
    
    # Build TypeScript
    if not args.skip_build:
        if not build_typescript():
            sys.exit(1)
    
    # Run report generation
    output_dir = args.output_dir or './reports'
    if not run_report_generation(args.csv_file, output_dir):
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("Report generation completed!")
    print(f"Reports saved to: {output_dir}")
    print("Token usage logs saved to: logs/token-usage/")

if __name__ == "__main__":
    main()
