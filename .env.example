# Google Cloud Configuration for Vertex AI (for product reports)
GOOGLE_CLOUD_PROJECT_ID=your-google-cloud-project-id
GOOGLE_CLOUD_LOCATION=us-central1
# GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json

# LLM Provider Configuration
LLM_PROVIDER=vertex-ai
LLM_MODEL=gemini-1.5-pro

# Alternative LLM Providers
#LLM_PROVIDER=gemini
GEMINI_API_KEY=

# Ollama Configuration
OLLAMA_HOST=http://localhost:11434

#LLM_MODEL=browser
#LLM_MODEL=gemini-2.0-flash

# For OpenAI API
# LLM_PROVIDER=openai
# OPENAI_API_KEY=your-api-key-here
# LLM_MODEL=gpt-3.5-turbo
# OPENAI_BASE_URL=https://api.openai.com/v1

# For DeepSeek
# LLM_PROVIDER=openai
# OPENAI_API_KEY=
# LLM_MODEL=deepseek-chat
# OPENAI_BASE_URL=https://api.deepseek.com

# General Configuration
LOG_DIR=./logs
SCREENSHOT_DIR=./screenshots
#PLAYWRIGHT_BROWSERS_PATH=C:\Users\<USER>\AppData\Local\Google\Chrome SxS\Application\chrome.exe
UNIVERSAL_SUBMIT_SELECTOR=enterKeyPress

# Browser Configuration
DATA_DIR=C:\Users\<USER>\AppData\Local\Google\Chrome\User Data
PLAYWRIGHT_BROWSERS_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe
HEADLESS=false
START_URL=https://www.duckduckgo.com/