import dotenv from 'dotenv';
import { BaseLLMProcessor, ConversationMessage } from "./BaseLLMProcessor.js";
import logger from '../../utils/logger.js';
import { TokenLogger } from '../../utils/tokenLogger.js';

dotenv.config();

// Configuration with environment variable fallbacks
const PROJECT_ID = process.env.GOOGLE_CLOUD_PROJECT_ID;
const LOCATION = process.env.GOOGLE_CLOUD_LOCATION || 'us-central1';
const MODEL = process.env.LLM_MODEL || 'gemini-1.5-pro';

interface VertexAIResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
    finishReason: string;
  }>;
  usageMetadata: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}

class VertexProcessor extends BaseLLMProcessor {
  private tokenLogger: TokenLogger;

  constructor() {
    super();
    this.tokenLogger = TokenLogger.getInstance();
    
    if (!PROJECT_ID) {
      logger.error('GOOGLE_CLOUD_PROJECT_ID is not defined in environment variables');
      throw new Error("GOOGLE_CLOUD_PROJECT_ID is not defined. Please set it in the environment variables.");
    }
  }

  protected async processPrompt(prompt: string, systemPrompt: string): Promise<string> {
    try {
      // Import Google Cloud AI Platform client
      const { PredictionServiceClient } = await import('@google-cloud/aiplatform');
      const client = new PredictionServiceClient();

      // Build the request payload for Vertex AI
      const messages = [
        { role: "system", content: systemPrompt },
        ...this.lastContext,
        { role: "user", content: prompt }
      ];

      // Convert messages to Vertex AI format
      const contents = messages.map(msg => ({
        role: msg.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: msg.content }]
      }));

      const request = {
        endpoint: `projects/${PROJECT_ID}/locations/${LOCATION}/publishers/google/models/${MODEL}`,
        instances: [{
          messages: contents
        }],
        parameters: {
          temperature: 0.1,
          maxOutputTokens: 8192,
          topP: 0.8,
          topK: 40
        }
      };

      logger.info('LLM request sent to Vertex AI', {
        model: MODEL,
        project: PROJECT_ID,
        location: LOCATION,
        contextLength: this.lastContext.length,
        promptLength: prompt.length,
      });

      // Make the prediction request
      const [response] = await client.predict(request);
      
      if (!response.predictions || response.predictions.length === 0) {
        throw new Error('No predictions returned from Vertex AI');
      }

      const prediction = response.predictions[0] as any;
      const vertexResponse = prediction as VertexAIResponse;

      // Extract response text
      let responseText = '';
      if (vertexResponse.candidates && vertexResponse.candidates.length > 0) {
        const candidate = vertexResponse.candidates[0];
        if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
          responseText = candidate.content.parts[0].text;
        }
      }

      if (!responseText) {
        throw new Error('No response text found in Vertex AI response');
      }

      // Log token usage
      if (vertexResponse.usageMetadata) {
        const usage = vertexResponse.usageMetadata;
        this.tokenLogger.logTokenUsage({
          promptTokens: usage.promptTokenCount,
          completionTokens: usage.candidatesTokenCount,
          totalTokens: usage.totalTokenCount,
          model: MODEL,
          provider: 'vertex-ai'
        });

        logger.info('Vertex AI token usage', {
          promptTokens: usage.promptTokenCount,
          completionTokens: usage.candidatesTokenCount,
          totalTokens: usage.totalTokenCount
        });
      }

      // Update conversation context
      this.updateContext(prompt, responseText);

      return responseText;

    } catch (error: any) {
      // Check for token limit errors
      if (error.message && (
          error.message.includes("maximum context length") || 
          error.message.includes("token") ||
          error.message.includes("content size") ||
          error.message.includes("too long") ||
          error.message.includes("quota")
        )) {
        logger.warn('Hit token limit in Vertex AI, pruning context and retrying', {
          error: error.message,
          contextLength: this.lastContext.length
        });
        
        // Axe half the context and try again
        this.pruneContextIfNeeded();
        return this.processPrompt(prompt, systemPrompt);
      }

      logger.error('Vertex AI LLM Error: ', error);
      throw new Error(`Error communicating with Vertex AI API: ${error.message}`);
    }
  }
}

// Export a singleton instance
export const vertexProcessor = new VertexProcessor();
