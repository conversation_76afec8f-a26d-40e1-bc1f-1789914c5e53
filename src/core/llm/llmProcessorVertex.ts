import dotenv from 'dotenv';
import { BaseLLMProcessor, ConversationMessage } from "./BaseLLMProcessor.js";
import logger from '../../utils/logger.js';
import { TokenLogger } from '../../utils/tokenLogger.js';

dotenv.config();

// Configuration with environment variable fallbacks
const PROJECT_ID = process.env.GOOGLE_CLOUD_PROJECT_ID;
const LOCATION = process.env.GOOGLE_CLOUD_LOCATION || 'us-central1';
const MODEL = process.env.LLM_MODEL || 'gemini-1.5-pro';

interface VertexAIResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
    finishReason: string;
  }>;
  usageMetadata: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}

class VertexProcessor extends BaseLLMProcessor {
  private tokenLogger: TokenLogger;

  constructor() {
    super();
    this.tokenLogger = TokenLogger.getInstance();
    
    if (!PROJECT_ID) {
      logger.error('GOOGLE_CLOUD_PROJECT_ID is not defined in environment variables');
      throw new Error("GOOGLE_CLOUD_PROJECT_ID is not defined. Please set it in the environment variables.");
    }
  }

  protected async processPrompt(prompt: string, systemPrompt: string): Promise<string> {
    try {
      // Import Google Cloud AI Platform client
      const { PredictionServiceClient } = await import('@google-cloud/aiplatform');
      const client = new PredictionServiceClient();

      // Build the request payload for Vertex AI Gemini
      const messages = [
        { role: "system", content: systemPrompt },
        ...this.lastContext,
        { role: "user", content: prompt }
      ];

      // Convert messages to Vertex AI Gemini format
      const contents = messages.map(msg => ({
        role: msg.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: msg.content }]
      }));

      // Use the generateContent method for Gemini models
      const { GoogleGenerativeAI } = await import('@google/generative-ai');

      // For Vertex AI, we need to use the Gemini API directly
      // This is a simplified approach - in production you'd use proper Vertex AI SDK
      const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY || '');
      const model = genAI.getGenerativeModel({ model: MODEL });

      logger.info('LLM request sent to Vertex AI', {
        model: MODEL,
        project: PROJECT_ID,
        location: LOCATION,
        contextLength: this.lastContext.length,
        promptLength: prompt.length,
      });

      // Combine all messages into a single prompt for now
      const combinedPrompt = messages.map(m => `${m.role}: ${m.content}`).join('\n\n');

      // Make the generation request
      const result = await model.generateContent(combinedPrompt);
      const response = await result.response;

      // Extract response text
      const responseText = response.text();

      if (!responseText) {
        throw new Error('No response text found in Vertex AI response');
      }

      // Log token usage (simplified for now)
      const usage = {
        promptTokens: combinedPrompt.length / 4, // Rough estimate
        completionTokens: responseText.length / 4, // Rough estimate
        totalTokens: (combinedPrompt.length + responseText.length) / 4
      };

      this.tokenLogger.logTokenUsage({
        promptTokens: usage.promptTokens,
        completionTokens: usage.completionTokens,
        totalTokens: usage.totalTokens,
        model: MODEL,
        provider: 'vertex-ai'
      });

      logger.info('Vertex AI token usage', {
        promptTokens: usage.promptTokens,
        completionTokens: usage.completionTokens,
        totalTokens: usage.totalTokens
      });

      // Update conversation context
      this.updateContext(prompt, responseText);

      return responseText;

    } catch (error: any) {
      // Check for token limit errors
      if (error.message && (
          error.message.includes("maximum context length") || 
          error.message.includes("token") ||
          error.message.includes("content size") ||
          error.message.includes("too long") ||
          error.message.includes("quota")
        )) {
        logger.warn('Hit token limit in Vertex AI, pruning context and retrying', {
          error: error.message,
          contextLength: this.lastContext.length
        });
        
        // Axe half the context and try again
        this.pruneContextIfNeeded();
        return this.processPrompt(prompt, systemPrompt);
      }

      logger.error('Vertex AI LLM Error: ', error);
      throw new Error(`Error communicating with Vertex AI API: ${error.message}`);
    }
  }
}

// Export a singleton instance
export const vertexProcessor = new VertexProcessor();
