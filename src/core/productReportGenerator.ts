import fs from 'fs';
import path from 'path';
import { GraphContext } from '../browserExecutor.js';
import { runStateMachine, registerState } from './automation/machine.js';
import { launchBrowser, createPage } from '../browserExecutor.js';
import { EbayProductMetadata, CSVProcessor } from '../utils/csvProcessor.js';
import { TokenLogger } from '../utils/tokenLogger.js';
import logger from '../utils/logger.js';
import { getAgentState } from '../utils/agentState.js';

export interface ProductReportResult {
  productId: string;
  success: boolean;
  reportPath?: string;
  error?: string;
  tokenUsage?: any;
}

export class ProductReportGenerator {
  private outputDirectory: string;
  private tokenLogger: TokenLogger;

  constructor(outputDirectory: string = './reports') {
    this.outputDirectory = outputDirectory;
    this.tokenLogger = TokenLogger.getInstance();
    this.ensureOutputDirectory();
  }

  private ensureOutputDirectory(): void {
    if (!fs.existsSync(this.outputDirectory)) {
      fs.mkdirSync(this.outputDirectory, { recursive: true });
    }
  }

  public async generateReportsFromCSV(csvFilePath: string): Promise<ProductReportResult[]> {
    try {
      // Validate CSV structure first
      await CSVProcessor.validateCSVStructure(csvFilePath);
      
      // Parse CSV
      const products = await CSVProcessor.parseCSV(csvFilePath);
      
      if (products.length === 0) {
        throw new Error('No valid products found in CSV file');
      }

      logger.info('Starting batch report generation', {
        csvFile: csvFilePath,
        totalProducts: products.length,
        outputDirectory: this.outputDirectory
      });

      const results: ProductReportResult[] = [];
      const batchId = `batch_${Date.now()}`;

      // Process each product sequentially
      for (let i = 0; i < products.length; i++) {
        const product = products[i];
        const productId = CSVProcessor.generateProductId(product);
        
        logger.info(`Processing product ${i + 1}/${products.length}`, {
          productId,
          title: product.title
        });

        try {
          const result = await this.generateSingleReport(product, productId);
          results.push(result);
          
          // Small delay between products to be respectful
          if (i < products.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        } catch (error) {
          logger.error(`Failed to generate report for product ${productId}`, { error });
          results.push({
            productId,
            success: false,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      // Generate batch summary
      this.tokenLogger.generateBatchSummary(batchId);
      this.generateBatchSummaryReport(results, batchId);

      logger.info('Batch report generation completed', {
        totalProducts: products.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length
      });

      return results;

    } catch (error) {
      logger.error('Failed to generate reports from CSV', { error, csvFilePath });
      throw error;
    }
  }

  private async generateSingleReport(product: EbayProductMetadata, productId: string): Promise<ProductReportResult> {
    // Start token tracking for this product
    this.tokenLogger.startProductSession(productId, product.title);

    try {
      // Create research goal with product metadata
      const researchGoal = this.buildResearchGoal(product);
      
      // Initialize browser context
      const ctx: GraphContext = {
        history: [],
        userGoal: researchGoal,
        successfulActions: [],
        lastActionSuccess: false,
        successCount: 0,
        milestones: [],
        recognizedMilestones: []
      };

      // Run the research automation
      await this.runResearchAutomation(ctx);

      // Extract the final report from the context
      const reportContent = this.extractReportFromContext(ctx, product);

      // Save the report
      const reportPath = await this.saveReport(productId, reportContent);

      // End token tracking
      const tokenUsage = this.tokenLogger.endProductSession();

      return {
        productId,
        success: true,
        reportPath,
        tokenUsage
      };

    } catch (error) {
      // End token tracking even on error
      this.tokenLogger.endProductSession();
      
      logger.error(`Report generation failed for product ${productId}`, { error });
      throw error;
    }
  }

  private buildResearchGoal(product: EbayProductMetadata): string {
    const metadataString = CSVProcessor.formatMetadataForPrompt(product);
    
    // Load the researcher prompt template
    const promptPath = path.join(process.cwd(), 'researcher-prompt.txt');
    let promptTemplate = '';
    
    try {
      promptTemplate = fs.readFileSync(promptPath, 'utf8');
    } catch (error) {
      logger.error('Failed to load researcher prompt template', { error });
      throw new Error('Could not load researcher prompt template');
    }

    // Replace metadata placeholders
    const researchGoal = promptTemplate
      .replace('{itemId}', product.itemId)
      .replace('{title}', product.title)
      .replace('{price}', product.price)
      .replace('{currency}', product.currency)
      .replace('{condition}', product.condition)
      .replace('{mainImage}', product.mainImage)
      .replace('{shortDescription}', product.shortDescription)
      .replace('{additionalImages}', product.additionalImages)
      .replace('{itemSpecifics}', product.itemSpecifics)
      .replace('{seller_username}', product.seller_username)
      .replace('{seller_feedback_pct}', product.seller_feedback_pct)
      .replace('{seller_feedback_count}', product.seller_feedback_count)
      .replace('{location_city}', product.location_city)
      .replace('{location_country}', product.location_country);

    return researchGoal;
  }

  private async runResearchAutomation(ctx: GraphContext): Promise<void> {
    // Initialize browser
    ctx.browser = await launchBrowser();
    ctx.page = await createPage(ctx.browser);
    ctx.startTime = Date.now();

    // Reset agent state
    const agentState = getAgentState();
    agentState.clearStop();

    try {
      // Run the state machine for research
      await runStateMachine(ctx);
    } finally {
      // Clean up browser
      if (ctx.browser) {
        await ctx.browser.close();
      }
    }
  }

  private extractReportFromContext(ctx: GraphContext, product: EbayProductMetadata): string {
    // Look for the final report in the context history
    // This is a simplified extraction - in practice, you might need more sophisticated parsing
    const history = ctx.history || [];
    
    // Find the last substantial response that looks like a report
    let reportContent = '';
    for (let i = history.length - 1; i >= 0; i--) {
      const entry = history[i];
      if (typeof entry === 'string' && entry.length > 500) {
        // This looks like a substantial report
        reportContent = entry;
        break;
      }
    }

    if (!reportContent) {
      // Fallback: create a basic report structure
      reportContent = `Product Report: ${product.title}

Product Overview
${product.title} - ${product.condition}
Price: ${product.price} ${product.currency}
Seller: ${product.seller_username}

Market Analysis
Listed at ${product.price} ${product.currency}

Reviews & Reputation
Seller feedback: ${product.seller_feedback_pct}% (${product.seller_feedback_count} reviews)

Issues and Concerns
No specific issues identified during automated research.

Seller and Listing Evaluation
Seller: ${product.seller_username}
Location: ${product.location_city}, ${product.location_country}

Conclusion
Basic product information extracted from listing data.

Citations
eBay listing data provided in CSV format.`;
    }

    return reportContent;
  }

  private async saveReport(productId: string, content: string): Promise<string> {
    const filename = `${productId}_report.txt`;
    const filepath = path.join(this.outputDirectory, filename);
    
    try {
      fs.writeFileSync(filepath, content, 'utf8');
      logger.info('Report saved successfully', { productId, filepath });
      return filepath;
    } catch (error) {
      logger.error('Failed to save report', { error, productId, filepath });
      throw error;
    }
  }

  private generateBatchSummaryReport(results: ProductReportResult[], batchId: string): void {
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    const summaryContent = `Batch Report Generation Summary
Batch ID: ${batchId}
Generated: ${new Date().toISOString()}

Total Products: ${results.length}
Successful: ${successful.length}
Failed: ${failed.length}

Successful Reports:
${successful.map(r => `- ${r.productId}: ${r.reportPath}`).join('\n')}

${failed.length > 0 ? `Failed Reports:
${failed.map(r => `- ${r.productId}: ${r.error}`).join('\n')}` : ''}

Token usage details can be found in the logs/token-usage directory.
`;

    const summaryPath = path.join(this.outputDirectory, `batch_summary_${batchId}.txt`);
    fs.writeFileSync(summaryPath, summaryContent, 'utf8');
    
    logger.info('Batch summary report generated', { summaryPath });
  }
}
