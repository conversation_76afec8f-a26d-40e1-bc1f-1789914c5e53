import fs from 'fs';
import path from 'path';
import logger from './logger.js';

export interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  model: string;
  provider: string;
  timestamp?: Date;
}

export interface ProductTokenSummary {
  productId: string;
  productTitle: string;
  totalTokens: number;
  totalCost: number;
  requestCount: number;
  usageDetails: TokenUsage[];
  startTime: Date;
  endTime?: Date;
}

export class TokenLogger {
  private static instance: TokenLogger;
  private currentProductSummary: ProductTokenSummary | null = null;
  private logDirectory: string;

  private constructor() {
    this.logDirectory = path.join(process.cwd(), 'logs', 'token-usage');
    this.ensureLogDirectory();
  }

  public static getInstance(): TokenLogger {
    if (!TokenLogger.instance) {
      TokenLogger.instance = new TokenLogger();
    }
    return TokenLogger.instance;
  }

  private ensureLogDirectory(): void {
    if (!fs.existsSync(this.logDirectory)) {
      fs.mkdirSync(this.logDirectory, { recursive: true });
    }
  }

  public startProductSession(productId: string, productTitle: string): void {
    if (this.currentProductSummary) {
      this.endProductSession();
    }

    this.currentProductSummary = {
      productId,
      productTitle,
      totalTokens: 0,
      totalCost: 0,
      requestCount: 0,
      usageDetails: [],
      startTime: new Date()
    };

    logger.info('Started token tracking for product', {
      productId,
      productTitle,
      startTime: this.currentProductSummary.startTime
    });
  }

  public logTokenUsage(usage: TokenUsage): void {
    if (!this.currentProductSummary) {
      logger.warn('Token usage logged without active product session');
      return;
    }

    const timestampedUsage = {
      ...usage,
      timestamp: new Date()
    };

    this.currentProductSummary.usageDetails.push(timestampedUsage);
    this.currentProductSummary.totalTokens += usage.totalTokens;
    this.currentProductSummary.requestCount++;

    // Calculate cost (rough estimates for Vertex AI)
    const cost = this.calculateCost(usage);
    this.currentProductSummary.totalCost += cost;

    logger.info('Token usage logged', {
      productId: this.currentProductSummary.productId,
      requestNumber: this.currentProductSummary.requestCount,
      tokens: usage.totalTokens,
      cumulativeTokens: this.currentProductSummary.totalTokens,
      estimatedCost: cost,
      cumulativeCost: this.currentProductSummary.totalCost
    });
  }

  private calculateCost(usage: TokenUsage): number {
    // Rough cost estimates for Vertex AI Gemini (as of 2024)
    // These are approximate and should be updated with current pricing
    const inputCostPer1K = 0.00025; // $0.00025 per 1K input tokens
    const outputCostPer1K = 0.0005;  // $0.0005 per 1K output tokens

    const inputCost = (usage.promptTokens / 1000) * inputCostPer1K;
    const outputCost = (usage.completionTokens / 1000) * outputCostPer1K;

    return inputCost + outputCost;
  }

  public endProductSession(): ProductTokenSummary | null {
    if (!this.currentProductSummary) {
      return null;
    }

    this.currentProductSummary.endTime = new Date();
    const summary = { ...this.currentProductSummary };

    // Save detailed log to file
    this.saveProductLog(summary);

    // Log summary
    logger.info('Product token session completed', {
      productId: summary.productId,
      productTitle: summary.productTitle,
      totalTokens: summary.totalTokens,
      totalCost: summary.totalCost,
      requestCount: summary.requestCount,
      duration: summary.endTime ? summary.endTime.getTime() - summary.startTime.getTime() : 0
    });

    this.currentProductSummary = null;
    return summary;
  }

  private saveProductLog(summary: ProductTokenSummary): void {
    const timestamp = summary.startTime.toISOString().replace(/[:.]/g, '-');
    const filename = `${summary.productId}_${timestamp}.json`;
    const filepath = path.join(this.logDirectory, filename);

    try {
      fs.writeFileSync(filepath, JSON.stringify(summary, null, 2));
      logger.info('Token usage log saved', { filepath });
    } catch (error) {
      logger.error('Failed to save token usage log', { error, filepath });
    }
  }

  public getCurrentSessionSummary(): ProductTokenSummary | null {
    return this.currentProductSummary ? { ...this.currentProductSummary } : null;
  }

  public generateBatchSummary(batchId: string): void {
    const logFiles = fs.readdirSync(this.logDirectory)
      .filter(file => file.endsWith('.json'))
      .sort();

    if (logFiles.length === 0) {
      logger.warn('No token usage logs found for batch summary');
      return;
    }

    const batchSummary = {
      batchId,
      generatedAt: new Date(),
      totalProducts: logFiles.length,
      totalTokens: 0,
      totalCost: 0,
      totalRequests: 0,
      products: [] as any[]
    };

    for (const file of logFiles) {
      try {
        const filepath = path.join(this.logDirectory, file);
        const productLog = JSON.parse(fs.readFileSync(filepath, 'utf8'));
        
        batchSummary.totalTokens += productLog.totalTokens;
        batchSummary.totalCost += productLog.totalCost;
        batchSummary.totalRequests += productLog.requestCount;
        
        batchSummary.products.push({
          productId: productLog.productId,
          productTitle: productLog.productTitle,
          tokens: productLog.totalTokens,
          cost: productLog.totalCost,
          requests: productLog.requestCount
        });
      } catch (error) {
        logger.error('Failed to read product log file', { file, error });
      }
    }

    // Save batch summary
    const batchFilename = `batch_summary_${batchId}_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
    const batchFilepath = path.join(this.logDirectory, batchFilename);
    
    try {
      fs.writeFileSync(batchFilepath, JSON.stringify(batchSummary, null, 2));
      logger.info('Batch token summary generated', {
        batchId,
        totalProducts: batchSummary.totalProducts,
        totalTokens: batchSummary.totalTokens,
        totalCost: batchSummary.totalCost,
        filepath: batchFilepath
      });
    } catch (error) {
      logger.error('Failed to save batch summary', { error, batchFilepath });
    }
  }
}
