import fs from 'fs';
import csv<PERSON><PERSON><PERSON> from 'csvtojson';
import logger from './logger.js';

export interface EbayProductMetadata {
  itemId: string;
  title: string;
  price: string;
  currency: string;
  condition: string;
  mainImage: string;
  shortDescription: string;
  additionalImages: string;
  itemSpecifics: string;
  seller_username: string;
  seller_feedback_pct: string;
  seller_feedback_count: string;
  location_city: string;
  location_country: string;
}

export class CSVProcessor {
  public static async parseCSV(filePath: string): Promise<EbayProductMetadata[]> {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error(`CSV file not found: ${filePath}`);
      }

      logger.info('Starting CSV parsing', { filePath });

      const jsonArray = await csvtojson().fromFile(filePath);
      
      if (jsonArray.length === 0) {
        throw new Error('CSV file is empty or has no valid data');
      }

      const products: EbayProductMetadata[] = jsonArray.map((row: any, index: number) => {
        try {
          return this.validateAndFormatProduct(row, index);
        } catch (error) {
          logger.warn(`Skipping invalid product at row ${index + 1}`, { error, row });
          return null;
        }
      }).filter((product): product is EbayProductMetadata => product !== null);

      logger.info('CSV parsing completed', {
        totalRows: jsonArray.length,
        validProducts: products.length,
        skippedRows: jsonArray.length - products.length
      });

      return products;

    } catch (error) {
      logger.error('Failed to parse CSV file', { error, filePath });
      throw error;
    }
  }

  private static validateAndFormatProduct(row: any, index: number): EbayProductMetadata {
    // Required fields validation
    if (!row.itemId || !row.title) {
      throw new Error(`Missing required fields (itemId or title) at row ${index + 1}`);
    }

    // Clean and format the data
    const product: EbayProductMetadata = {
      itemId: this.cleanString(row.itemId),
      title: this.cleanString(row.title),
      price: this.cleanString(row.price) || 'N/A',
      currency: this.cleanString(row.currency) || 'USD',
      condition: this.cleanString(row.condition) || 'N/A',
      mainImage: this.cleanString(row.mainImage) || 'N/A',
      shortDescription: this.cleanString(row.shortDescription) || 'N/A',
      additionalImages: this.cleanString(row.additionalImages) || 'N/A',
      itemSpecifics: this.cleanString(row.itemSpecifics) || 'N/A',
      seller_username: this.cleanString(row.seller_username) || 'N/A',
      seller_feedback_pct: this.cleanString(row.seller_feedback_pct) || 'N/A',
      seller_feedback_count: this.cleanString(row.seller_feedback_count) || 'N/A',
      location_city: this.cleanString(row.location_city) || 'N/A',
      location_country: this.cleanString(row.location_country) || 'N/A'
    };

    return product;
  }

  private static cleanString(value: any): string {
    if (value === null || value === undefined) {
      return '';
    }
    
    return String(value).trim();
  }

  public static formatMetadataForPrompt(product: EbayProductMetadata): string {
    return `itemId: ${product.itemId}
title: ${product.title}
price: ${product.price} ${product.currency}
condition: ${product.condition}
mainImage: ${product.mainImage}
shortDescription: ${product.shortDescription}
additionalImages: ${product.additionalImages}
itemSpecifics: ${product.itemSpecifics}
seller_username: ${product.seller_username}
seller_feedback_pct: ${product.seller_feedback_pct}
seller_feedback_count: ${product.seller_feedback_count}
location_city: ${product.location_city}
location_country: ${product.location_country}`;
  }

  public static generateProductId(product: EbayProductMetadata): string {
    // Create a safe filename from itemId and title
    const safeItemId = product.itemId.replace(/[^a-zA-Z0-9]/g, '_');
    const safeTitle = product.title
      .substring(0, 50) // Limit length
      .replace(/[^a-zA-Z0-9\s]/g, '') // Remove special chars
      .replace(/\s+/g, '_') // Replace spaces with underscores
      .toLowerCase();
    
    return `${safeItemId}_${safeTitle}`;
  }

  public static validateCSVStructure(filePath: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      csvtojson()
        .fromFile(filePath)
        .then((jsonArray) => {
          if (jsonArray.length === 0) {
            reject(new Error('CSV file is empty'));
            return;
          }

          const firstRow = jsonArray[0];
          const requiredFields = ['itemId', 'title'];
          const expectedFields = [
            'itemId', 'title', 'price', 'currency', 'condition', 
            'mainImage', 'shortDescription', 'additionalImages', 
            'itemSpecifics', 'seller_username', 'seller_feedback_pct', 
            'seller_feedback_count', 'location_city', 'location_country'
          ];

          // Check for required fields
          for (const field of requiredFields) {
            if (!(field in firstRow)) {
              reject(new Error(`Missing required field: ${field}`));
              return;
            }
          }

          // Log available fields vs expected
          const availableFields = Object.keys(firstRow);
          const missingFields = expectedFields.filter(field => !availableFields.includes(field));
          
          if (missingFields.length > 0) {
            logger.warn('Some expected fields are missing from CSV', {
              missingFields,
              availableFields
            });
          }

          logger.info('CSV structure validation passed', {
            totalRows: jsonArray.length,
            availableFields,
            missingFields
          });

          resolve(true);
        })
        .catch(reject);
    });
  }
}
