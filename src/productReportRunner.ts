import './utils/logger.js';
import dotenv from "dotenv";
import { ProductReportGenerator } from "./core/productReportGenerator.js";
import logger from './utils/logger.js';
import path from 'path';

dotenv.config();

// Ensure Vertex AI is configured
if (!process.env.GOOGLE_CLOUD_PROJECT_ID) {
  console.error('Error: GOOGLE_CLOUD_PROJECT_ID environment variable is required');
  process.exit(1);
}

// Set LLM provider to Vertex AI
process.env.LLM_PROVIDER = 'vertex-ai';

export async function runProductReports(csvFilePath: string, outputDir?: string): Promise<void> {
  try {
    // Validate input file
    if (!csvFilePath) {
      throw new Error('CSV file path is required');
    }

    // Resolve absolute path
    const absoluteCsvPath = path.resolve(csvFilePath);
    
    // Set up output directory
    const outputDirectory = outputDir || path.join(process.cwd(), 'reports');
    
    console.log(`Starting eBay Product Report Generation`);
    console.log(`CSV File: ${absoluteCsvPath}`);
    console.log(`Output Directory: ${outputDirectory}`);
    console.log(`LLM Provider: Vertex AI`);
    console.log(`Project ID: ${process.env.GOOGLE_CLOUD_PROJECT_ID}`);
    console.log('---');

    // Initialize report generator
    const generator = new ProductReportGenerator(outputDirectory);
    
    // Generate reports
    const results = await generator.generateReportsFromCSV(absoluteCsvPath);
    
    // Print summary
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    console.log('\n=== BATCH COMPLETION SUMMARY ===');
    console.log(`Total Products Processed: ${results.length}`);
    console.log(`Successful Reports: ${successful.length}`);
    console.log(`Failed Reports: ${failed.length}`);
    
    if (successful.length > 0) {
      console.log('\nSuccessful Reports:');
      successful.forEach(result => {
        console.log(`  ✓ ${result.productId}`);
        if (result.tokenUsage) {
          console.log(`    Tokens: ${result.tokenUsage.totalTokens}, Cost: $${result.tokenUsage.totalCost.toFixed(4)}`);
        }
      });
    }
    
    if (failed.length > 0) {
      console.log('\nFailed Reports:');
      failed.forEach(result => {
        console.log(`  ✗ ${result.productId}: ${result.error}`);
      });
    }
    
    console.log(`\nReports saved to: ${outputDirectory}`);
    console.log(`Token usage logs saved to: logs/token-usage/`);
    
  } catch (error) {
    console.error('Fatal error in product report generation:', error);
    logger.error('Fatal error in product report generation', { error });
    process.exit(1);
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const csvFile = process.argv[2];
  const outputDir = process.argv[3];
  
  if (!csvFile) {
    console.error('Usage: npm run product-reports <csv-file> [output-directory]');
    console.error('Example: npm run product-reports products.csv ./my-reports');
    process.exit(1);
  }
  
  runProductReports(csvFile, outputDir)
    .then(() => {
      console.log('\nProduct report generation completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Product report generation failed:', error);
      process.exit(1);
    });
}
